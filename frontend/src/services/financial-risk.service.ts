import axiosInstance from './axios.config';

// Types pour l'évaluation des risques financiers
export interface FinancialRisk {
  id: number;
  detection: number;
  detection_info: {
    id: number;
    type: string;
    confidence_score: number;
    coordinates: string;
  };
  area_hectares: number;
  cost_per_hectare: number;
  estimated_loss: number;
  sensitive_zone_distance_km: number;
  occurrence_count: number;
  risk_level: string;
  created_at: string;
}

export interface FinancialRiskProfile {
  id: string;
  region_id: number;
  region_name: string;
  total_risk_score: number;
  risk_level: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  last_assessment_date: string;
  factors: {
    mining_activity_risk: number;
    environmental_degradation: number;
    legal_compliance_risk: number;
    economic_impact: number;
    social_impact: number;
  };
  financial_metrics: {
    estimated_annual_loss: number;
    potential_fines: number;
    remediation_costs: number;
    lost_revenue: number;
    insurance_claims: number;
  };
  trend: 'IMPROVING' | 'STABLE' | 'DETERIORATING';
  recommendations: string[];
  next_review_date: string;
}

export interface RiskFactorConfig {
  id: string;
  name: string;
  description: string;
  weight: number;
  calculation_method: 'AUTOMATIC' | 'MANUAL' | 'HYBRID';
  data_sources: string[];
  update_frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY';
  is_active: boolean;
}

export interface FinancialImpactCalculation {
  scenario: 'BEST_CASE' | 'MOST_LIKELY' | 'WORST_CASE';
  timeframe_months: number;
  assumptions: {
    gold_price_per_gram: number;
    environmental_multiplier: number;
    legal_penalty_rate: number;
    remediation_cost_per_hectare: number;
    productivity_loss_percentage: number;
  };
  results: {
    total_estimated_impact: number;
    breakdown: {
      direct_losses: number;
      indirect_losses: number;
      regulatory_costs: number;
      opportunity_costs: number;
    };
    confidence_interval: {
      lower_bound: number;
      upper_bound: number;
    };
  };
}

export interface RiskMitigationPlan {
  id: string;
  risk_profile_id: string;
  title: string;
  description: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  estimated_cost: number;
  estimated_savings: number;
  roi_percentage: number;
  implementation_timeline: string;
  responsible_department: string;
  status: 'PLANNED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  milestones: Array<{
    title: string;
    due_date: string;
    status: 'PENDING' | 'COMPLETED';
    cost: number;
  }>;
  created_at: string;
  updated_at: string;
}

export interface RiskDashboardData {
  overview: {
    total_risk_exposure: number;
    high_risk_regions: number;
    active_mitigation_plans: number;
    potential_annual_savings: number;
  };
  risk_distribution: Array<{
    risk_level: string;
    count: number;
    percentage: number;
  }>;
  top_risk_factors: Array<{
    factor: string;
    impact_score: number;
    affected_regions: number;
  }>;
  financial_trends: Array<{
    month: string;
    estimated_losses: number;
    mitigation_savings: number;
    net_impact: number;
  }>;
  regional_comparison: Array<{
    region: string;
    risk_score: number;
    financial_impact: number;
    trend: string;
  }>;
}

class FinancialRiskService {
  // Méthodes existantes (compatibilité)
  async getFinancialRisks(params?: any): Promise<{ count: number; results: FinancialRisk[] }> {
    try {
      const response = await axiosInstance.get('/financial-risks/', { params });
      return response.data;
    } catch (error) {
      console.error('Erreur récupération risques financiers:', error);
      throw new Error('Impossible de récupérer les risques financiers');
    }
  }

  async getFinancialRisk(id: number): Promise<FinancialRisk> {
    try {
      const response = await axiosInstance.get(`/financial-risks/${id}/`);
      return response.data;
    } catch (error) {
      console.error('Erreur récupération risque financier:', error);
      throw new Error('Impossible de récupérer le risque financier');
    }
  }

  async getHighImpactRisks(): Promise<{
    count: number;
    total_estimated_loss: number;
    results: FinancialRisk[];
  }> {
    try {
      const response = await axiosInstance.get('/financial-risks/high-impact/');
      return response.data;
    } catch (error) {
      console.error('Erreur récupération risques à fort impact:', error);
      throw new Error('Impossible de récupérer les risques à fort impact');
    }
  }

  // ========== NOUVELLES MÉTHODES SPRINT 6 ==========

  // Récupération des profils de risque par région
  async getRiskProfiles(): Promise<FinancialRiskProfile[]> {
    try {
      const response = await axiosInstance.get('/financial-risk/profiles/');
      return response.data.results || response.data;
    } catch (error) {
      console.error('Erreur récupération profils de risque:', error);
      throw new Error('Impossible de récupérer les profils de risque');
    }
  }

  // Récupération d'un profil de risque spécifique
  async getRiskProfile(profileId: string): Promise<FinancialRiskProfile> {
    try {
      const response = await axiosInstance.get(`/financial-risk/profiles/${profileId}/`);
      return response.data;
    } catch (error) {
      console.error('Erreur récupération profil de risque:', error);
      throw new Error('Impossible de récupérer le profil de risque');
    }
  }

  // Calcul d'impact financier avec scénarios
  async calculateFinancialImpact(
    regionId: number,
    scenario: 'BEST_CASE' | 'MOST_LIKELY' | 'WORST_CASE',
    timeframeMonths: number = 12,
    customAssumptions?: Partial<FinancialImpactCalculation['assumptions']>
  ): Promise<FinancialImpactCalculation> {
    try {
      const response = await axiosInstance.post('/financial-risk/calculate-impact/', {
        region_id: regionId,
        scenario,
        timeframe_months: timeframeMonths,
        custom_assumptions: customAssumptions,
      });
      return response.data;
    } catch (error) {
      console.error('Erreur calcul impact financier:', error);
      throw new Error('Impossible de calculer l\'impact financier');
    }
  }

  // Gestion des facteurs de risque
  async getRiskFactors(): Promise<RiskFactorConfig[]> {
    try {
      const response = await axiosInstance.get('/financial-risk/factors/');
      return response.data.results || response.data;
    } catch (error) {
      console.error('Erreur récupération facteurs de risque:', error);
      throw new Error('Impossible de récupérer les facteurs de risque');
    }
  }

  async updateRiskFactor(factorId: string, updates: Partial<RiskFactorConfig>): Promise<RiskFactorConfig> {
    try {
      const response = await axiosInstance.patch(`/financial-risk/factors/${factorId}/`, updates);
      return response.data;
    } catch (error) {
      console.error('Erreur mise à jour facteur de risque:', error);
      throw new Error('Impossible de mettre à jour le facteur de risque');
    }
  }

  // Gestion des plans de mitigation
  async getMitigationPlans(riskProfileId?: string): Promise<RiskMitigationPlan[]> {
    try {
      const params = riskProfileId ? { risk_profile_id: riskProfileId } : {};
      const response = await axiosInstance.get('/financial-risk/mitigation-plans/', { params });
      return response.data.results || response.data;
    } catch (error) {
      console.error('Erreur récupération plans de mitigation:', error);
      throw new Error('Impossible de récupérer les plans de mitigation');
    }
  }

  async createMitigationPlan(plan: Omit<RiskMitigationPlan, 'id' | 'created_at' | 'updated_at'>): Promise<RiskMitigationPlan> {
    try {
      const response = await axiosInstance.post('/financial-risk/mitigation-plans/', plan);
      return response.data;
    } catch (error) {
      console.error('Erreur création plan de mitigation:', error);
      throw new Error('Impossible de créer le plan de mitigation');
    }
  }

  async updateMitigationPlan(planId: string, updates: Partial<RiskMitigationPlan>): Promise<RiskMitigationPlan> {
    try {
      const response = await axiosInstance.patch(`/financial-risk/mitigation-plans/${planId}/`, updates);
      return response.data;
    } catch (error) {
      console.error('Erreur mise à jour plan de mitigation:', error);
      throw new Error('Impossible de mettre à jour le plan de mitigation');
    }
  }

  async deleteMitigationPlan(planId: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axiosInstance.delete(`/financial-risk/mitigation-plans/${planId}/`);
      return response.data;
    } catch (error) {
      console.error('Erreur suppression plan de mitigation:', error);
      throw new Error('Impossible de supprimer le plan de mitigation');
    }
  }

  // Données du tableau de bord des risques
  async getRiskDashboardData(period?: 'week' | 'month' | 'quarter' | 'year'): Promise<RiskDashboardData> {
    try {
      const params = period ? { period } : {};
      const response = await axiosInstance.get('/financial-risk/dashboard/', { params });
      return response.data;
    } catch (error) {
      console.error('Erreur récupération données tableau de bord:', error);
      throw new Error('Impossible de récupérer les données du tableau de bord');
    }
  }

  // Génération de rapport de risque complet
  async generateRiskReport(
    regionIds?: number[],
    includeProjections: boolean = true,
    includeMitigationPlans: boolean = true
  ): Promise<{
    report_id: string;
    download_url: string;
    expires_at: string;
  }> {
    try {
      const response = await axiosInstance.post('/financial-risk/generate-report/', {
        region_ids: regionIds,
        include_projections: includeProjections,
        include_mitigation_plans: includeMitigationPlans,
      });
      return response.data;
    } catch (error) {
      console.error('Erreur génération rapport de risque:', error);
      throw new Error('Impossible de générer le rapport de risque');
    }
  }

  // Simulation de scénarios de risque
  async simulateRiskScenario(
    regionId: number,
    scenarioChanges: {
      mining_activity_change?: number;
      environmental_protection_level?: number;
      enforcement_strength?: number;
      economic_conditions?: number;
    }
  ): Promise<{
    original_risk_score: number;
    simulated_risk_score: number;
    risk_change_percentage: number;
    financial_impact_change: number;
    recommendations: string[];
  }> {
    try {
      const response = await axiosInstance.post('/financial-risk/simulate-scenario/', {
        region_id: regionId,
        scenario_changes: scenarioChanges,
      });
      return response.data;
    } catch (error) {
      console.error('Erreur simulation scénario de risque:', error);
      throw new Error('Impossible de simuler le scénario de risque');
    }
  }

  // Alertes de risque automatiques
  async getRiskAlerts(): Promise<Array<{
    id: string;
    region: string;
    risk_type: string;
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    message: string;
    financial_impact: number;
    created_at: string;
    is_acknowledged: boolean;
  }>> {
    try {
      const response = await axiosInstance.get('/financial-risk/alerts/');
      return response.data.results || response.data;
    } catch (error) {
      console.error('Erreur récupération alertes de risque:', error);
      throw new Error('Impossible de récupérer les alertes de risque');
    }
  }

  async acknowledgeRiskAlert(alertId: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axiosInstance.patch(`/financial-risk/alerts/${alertId}/acknowledge/`);
      return response.data;
    } catch (error) {
      console.error('Erreur accusé réception alerte de risque:', error);
      throw new Error('Impossible d\'accuser réception de l\'alerte');
    }
  }
}

export default new FinancialRiskService();