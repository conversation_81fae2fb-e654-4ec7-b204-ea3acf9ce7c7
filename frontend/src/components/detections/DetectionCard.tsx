import React from 'react';
import { motion } from 'framer-motion';
import {
  MapPinIcon,
  CalendarIcon,
  EyeIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';
import { <PERSON>, But<PERSON>, Badge } from '../ui';
import { usePermissions } from '../../hooks/usePermissions';
import {
  Detection,
  DETECTION_TYPE_LABELS,
  VALIDATION_STATUS_LABELS,
  DETECTION_TYPE_COLORS,
  VALIDATION_STATUS_COLORS,
  getConfidenceLevel,
  getDetectionTypeIcon,
  getValidationStatusIcon,
} from '../../types/detection.types';

interface DetectionCardProps {
  detection: Detection;
  onViewDetails: (detection: Detection) => void;
  onValidate?: (detection: Detection, status: string) => void;
  isLoading?: boolean;
}

export const DetectionCard: React.FC<DetectionCardProps> = ({
  detection,
  onViewDetails,
  onValidate,
  isLoading = false,
}) => {
  const permissions = usePermissions();
  const confidenceLevel = getConfidenceLevel(detection.confidence_score);

  const canValidate = permissions.canValidateDetections() &&
                     detection.validation_status === 'DETECTED';

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -2 }}
      transition={{ duration: 0.2 }}
    >
      <Card className="h-full flex flex-col hover:shadow-lg transition-shadow">
        {/* En-tête avec type et confiance */}
        <div className="p-4 border-b border-slate-200">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2">
              <span className="text-lg">
                {getDetectionTypeIcon(detection.detection_type)}
              </span>
              <h3 className="font-semibold text-slate-900">
                {DETECTION_TYPE_LABELS[detection.detection_type]}
              </h3>
            </div>
            <Badge
              variant={confidenceLevel.color as any}
              className="text-xs"
            >
              {(detection.confidence_score * 100).toFixed(0)}%
            </Badge>
          </div>

          <div className="flex items-center justify-between">
            <Badge
              variant={VALIDATION_STATUS_COLORS[detection.validation_status] as any}
              className="text-xs"
            >
              {getValidationStatusIcon(detection.validation_status)} {VALIDATION_STATUS_LABELS[detection.validation_status]}
            </Badge>
            <span className="text-xs text-slate-500">
              ID: {detection.id}
            </span>
          </div>
        </div>

        {/* Contenu principal */}
        <div className="p-4 flex-1">
          {/* Localisation */}
          <div className="flex items-center space-x-2 mb-3">
            <MapPinIcon className="w-4 h-4 text-slate-400" />
            <span className="text-sm text-slate-600">
              {detection.region_name}
            </span>
            <span className="text-xs text-slate-400">
              ({detection.latitude.toFixed(4)}, {detection.longitude.toFixed(4)})
            </span>
          </div>

          {/* Date de détection */}
          <div className="flex items-center space-x-2 mb-3">
            <CalendarIcon className="w-4 h-4 text-slate-400" />
            <span className="text-sm text-slate-600">
              Détectée le {formatDate(detection.detection_date)}
            </span>
          </div>

          {/* Surface */}
          <div className="mb-3">
            <span className="text-sm text-slate-600">
              Surface estimée: <span className="font-medium">{detection.area_hectares.toFixed(2)} ha</span>
            </span>
          </div>

          {/* Indices spectraux */}
          <div className="grid grid-cols-3 gap-2 mb-4">
            <div className="text-center">
              <div className="text-xs text-slate-500">NDVI</div>
              <div className="text-sm font-medium text-green-600">
                {detection.ndvi_anomaly_score?.toFixed(2) || 'N/A'}
              </div>
            </div>
            <div className="text-center">
              <div className="text-xs text-slate-500">NDWI</div>
              <div className="text-sm font-medium text-blue-600">
                {detection.ndwi_anomaly_score?.toFixed(2) || 'N/A'}
              </div>
            </div>
            <div className="text-center">
              <div className="text-xs text-slate-500">NDTI</div>
              <div className="text-sm font-medium text-orange-600">
                {detection.ndti_anomaly_score?.toFixed(2) || 'N/A'}
              </div>
            </div>
          </div>

          {/* Informations de validation */}
          {detection.validated_by_name && (
            <div className="text-xs text-slate-500 mb-3">
              <div className="flex items-center space-x-1">
                <ClockIcon className="w-3 h-3" />
                <span>
                  Validée par {detection.validated_by_name}
                </span>
              </div>
              {detection.validated_at && (
                <div className="mt-1">
                  le {formatDateTime(detection.validated_at)}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="p-4 border-t border-slate-200">
          <div className="flex items-center justify-between space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onViewDetails(detection)}
              className="flex items-center space-x-1"
            >
              <EyeIcon className="w-4 h-4" />
              <span>Détails</span>
            </Button>

            {canValidate && onValidate && (
              <div className="flex space-x-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onValidate(detection, 'VALIDATED')}
                  disabled={isLoading}
                  className="flex items-center space-x-1 text-green-600 hover:text-green-700 hover:bg-green-50"
                >
                  <CheckCircleIcon className="w-4 h-4" />
                  <span>Valider</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onValidate(detection, 'FALSE_POSITIVE')}
                  disabled={isLoading}
                  className="flex items-center space-x-1 text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <XCircleIcon className="w-4 h-4" />
                  <span>Rejeter</span>
                </Button>
              </div>
            )}
          </div>
        </div>
      </Card>
    </motion.div>
  );
};
