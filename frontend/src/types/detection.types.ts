// Nouveau fichier de types pour les détections - structure simplifiée

// Types de base
export type DetectionType = 'MINING_SITE' | 'WATER_POLLUTION' | 'DEFORESTATION' | 'SOIL_DISTURBANCE';

export type ValidationStatus = 'DETECTED' | 'VALIDATED' | 'CONFIRMED' | 'FALSE_POSITIVE';

// Interface principale Detection
export interface Detection {
  id: number;
  image: number;
  image_name: string;
  image_capture_date: string;
  region: number;
  region_name: string;
  latitude: number;
  longitude: number;
  detection_type: DetectionType;
  confidence_score: number;
  area_hectares: number;
  ndvi_anomaly_score: number | null;
  ndwi_anomaly_score: number | null;
  ndti_anomaly_score: number | null;
  validation_status: ValidationStatus;
  validated_by: number | null;
  validated_by_name: string | null;
  validated_at: string | null;
  detection_date: string;
  algorithm_version: string;
}

// Interface pour les filtres
export interface DetectionFilters {
  search?: string;
  detection_type?: DetectionType | 'all';
  validation_status?: ValidationStatus | 'all';
  region?: number | 'all';
  confidence_min?: number;
  confidence_max?: number;
  date_from?: string;
  date_to?: string;
  ordering?: string;
}

// Interfaces pour les réponses API
export interface DetectionResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Detection[];
}

export interface ValidationResponse {
  id: number;
  validation_status: ValidationStatus;
  validated_by: number;
  validated_at: string;
  comment?: string;
}

// Constantes
export const DETECTION_TYPE_LABELS: Record<DetectionType, string> = {
  MINING_SITE: 'Site minier',
  WATER_POLLUTION: 'Pollution eau',
  DEFORESTATION: 'Déforestation',
  SOIL_DISTURBANCE: 'Perturbation sol',
};

export const VALIDATION_STATUS_LABELS: Record<ValidationStatus, string> = {
  DETECTED: 'Détecté',
  VALIDATED: 'Validé',
  CONFIRMED: 'Confirmé',
  FALSE_POSITIVE: 'Faux positif',
};
