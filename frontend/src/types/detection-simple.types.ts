// Fichier de types simplifié pour les détections

export type DetectionType = 'MINING_SITE' | 'WATER_POLLUTION' | 'DEFORESTATION' | 'SOIL_DISTURBANCE';

export type ValidationStatus = 'DETECTED' | 'VALIDATED' | 'CONFIRMED' | 'FALSE_POSITIVE';

export interface Detection {
  id: number;
  image: number;
  image_name: string;
  image_capture_date: string;
  region: number;
  region_name: string;
  latitude: number;
  longitude: number;
  detection_type: DetectionType;
  confidence_score: number;
  area_hectares: number;
  ndvi_anomaly_score: number | null;
  ndwi_anomaly_score: number | null;
  ndti_anomaly_score: number | null;
  validation_status: ValidationStatus;
  validated_by: number | null;
  validated_by_name: string | null;
  validated_at: string | null;
  detection_date: string;
  algorithm_version: string;
}

export interface DetectionFilters {
  search?: string;
  detection_type?: DetectionType | 'all';
  validation_status?: ValidationStatus | 'all';
  region?: number | 'all';
  confidence_min?: number;
  confidence_max?: number;
  date_from?: string;
  date_to?: string;
  ordering?: string;
}

export interface DetectionResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Detection[];
}

export interface ValidationResponse {
  id: number;
  validation_status: ValidationStatus;
  validated_by: number;
  validated_at: string;
  comment?: string;
}

export const DETECTION_TYPE_LABELS: Record<DetectionType, string> = {
  MINING_SITE: 'Site minier',
  WATER_POLLUTION: 'Pollution eau',
  DEFORESTATION: 'Déforestation',
  SOIL_DISTURBANCE: 'Perturbation sol',
};

export const VALIDATION_STATUS_LABELS: Record<ValidationStatus, string> = {
  DETECTED: 'Détecté',
  VALIDATED: 'Validé',
  CONFIRMED: 'Confirmé',
  FALSE_POSITIVE: 'Faux positif',
};

export const DETECTION_TYPE_COLORS: Record<DetectionType, string> = {
  MINING_SITE: 'orange',
  WATER_POLLUTION: 'blue',
  DEFORESTATION: 'red',
  SOIL_DISTURBANCE: 'yellow',
};

export const VALIDATION_STATUS_COLORS: Record<ValidationStatus, string> = {
  DETECTED: 'slate',
  VALIDATED: 'blue',
  CONFIRMED: 'green',
  FALSE_POSITIVE: 'red',
};

export const CONFIDENCE_LEVELS = {
  HIGH: { min: 0.8, max: 1.0, label: 'Élevée', color: 'green' },
  MEDIUM: { min: 0.5, max: 0.8, label: 'Moyenne', color: 'yellow' },
  LOW: { min: 0.0, max: 0.5, label: 'Faible', color: 'red' },
} as const;

export const getConfidenceLevel = (score: number) => {
  if (score >= CONFIDENCE_LEVELS.HIGH.min) return CONFIDENCE_LEVELS.HIGH;
  if (score >= CONFIDENCE_LEVELS.MEDIUM.min) return CONFIDENCE_LEVELS.MEDIUM;
  return CONFIDENCE_LEVELS.LOW;
};

export const getDetectionTypeIcon = (type: DetectionType): string => {
  const icons: Record<DetectionType, string> = {
    MINING_SITE: '⛏️',
    WATER_POLLUTION: '💧',
    DEFORESTATION: '🌳',
    SOIL_DISTURBANCE: '🏗️',
  };
  return icons[type];
};

export const getValidationStatusIcon = (status: ValidationStatus): string => {
  const icons: Record<ValidationStatus, string> = {
    DETECTED: '🔍',
    VALIDATED: '✅',
    CONFIRMED: '🎯',
    FALSE_POSITIVE: '❌',
  };
  return icons[status];
};

export interface DetectionStats {
  total: number;
  by_type: Record<DetectionType, number>;
  by_status: Record<ValidationStatus, number>;
  by_confidence: {
    high: number;
    medium: number;
    low: number;
  };
  recent_count: number;
}

export interface ValidationAction {
  detection_id: number;
  validation_status: ValidationStatus;
  comment?: string;
}
